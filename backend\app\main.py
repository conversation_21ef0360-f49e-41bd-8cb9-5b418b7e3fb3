import json
import logging
import time
from typing import Any, Dict

from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from .schemas import ScoreRequest, ScoreResponse
from .prompt_templates import score_system_prompt, build_user_prompt
from .llm_client import llm_client
from .config import settings

logger = logging.getLogger("app")
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(name)s: %(message)s")

app = FastAPI(title="IELTS/TOEFL Writing Coach API", version="0.1.0")

# CORS: 允许本地开发跨域，建议上线后收紧白名单
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class Health(BaseModel):
    status: str
    model: str
    base_url: str


@app.get("/health", response_model=Health)
async def health():
    return Health(status="ok", model=settings.model, base_url=settings.base_url)


def _mock_score(req: ScoreRequest) -> ScoreResponse:
    """稳定的模拟评分结果，保障前端演示与回退。"""
    dims = (
        [
            {"key": "TR", "name": "Task Response", "score": 6.5, "max_score": 9, "rationale": "Addresses most parts, position present but not fully extended."},
            {"key": "CC", "name": "Coherence and Cohesion", "score": 6.0, "max_score": 9, "rationale": "Generally logical progression; some abrupt transitions."},
            {"key": "LR", "name": "Lexical Resource", "score": 6.0, "max_score": 9, "rationale": "Adequate range; occasional repetition and inaccurate collocations."},
            {"key": "GRA", "name": "Grammatical Range and Accuracy", "score": 6.0, "max_score": 9, "rationale": "Some complex structures with noticeable errors."},
        ]
        if req.exam == "ielts"
        else [
            {"key": "ORG", "name": "Organization", "score": 3.5, "max_score": 5, "rationale": "Clear thesis; transitions could be smoother."},
            {"key": "DEV", "name": "Development", "score": 3.0, "max_score": 5, "rationale": "Examples relevant but underdeveloped."},
            {"key": "LANG", "name": "Language Use", "score": 3.0, "max_score": 5, "rationale": "Some awkward phrases; limited variety."},
            {"key": "GRAM", "name": "Grammar & Usage", "score": 3.0, "max_score": 5, "rationale": "Frequent minor errors; a few major ones."},
        ]
    )
    issues = [
        {"sentence_index": 0, "start_char": 0, "end_char": 20, "category": "grammar", "severity": "minor", "comment": "主谓一致问题。", "evidence": "People is"},
        {"sentence_index": 2, "start_char": 120, "end_char": 160, "category": "coherence", "severity": "major", "comment": "衔接不清，建议添加过渡。", "evidence": "On the other hand"},
    ]
    suggestions = [
        {"level": "sentence", "target_indices": [0], "rewrite": "People are increasingly concerned about...", "rationale": "修正主谓一致并引入主题。"},
        {"level": "paragraph", "target_indices": [1], "rewrite": "Add a transitional sentence linking the example to the thesis.", "rationale": "增强段间逻辑连贯。"},
    ]
    overall = 6.0 if req.exam == "ielts" else 3.0
    return ScoreResponse(
        exam=req.exam,
        overall_score=overall,
        dimensions=dims,  # type: ignore
        issues=issues,  # type: ignore
        suggestions=suggestions,  # type: ignore
        meta={"model": settings.model, "latency_ms": 50, "mode": "mock"},
    )


@app.post("/api/submissions/score", response_model=ScoreResponse)
async def score(req: ScoreRequest, request: Request):
    """评分主入口：优先真实 LLM，失败或 debug_mock 时回退到 mock。"""
    client_ip = request.client.host if request.client else "?"
    logger.info("score called ip=%s exam=%s mock=%s", client_ip, req.exam, req.debug_mock)

    if req.debug_mock:
        return _mock_score(req)

    t0 = time.time()
    system = score_system_prompt(req.exam)
    user = build_user_prompt(req.exam, req.prompt_text, req.essay_text)
    messages = [
        {"role": "system", "content": system},
        {"role": "user", "content": user},
    ]

    try:
        resp = await llm_client.chat(messages, response_format={"type": "json_object"})
        content = resp["choices"][0]["message"]["content"]
        latency_ms = int((time.time() - t0) * 1000)
        logger.info("llm ok latency_ms=%s", latency_ms)
    except Exception as e:
        logger.error("llm error: %s", e)
        # 回退 mock，避免前端阻塞
        return _mock_score(req)

    try:
        data: Dict[str, Any] = json.loads(content)
        latency_ms = int((time.time() - t0) * 1000)
        data.setdefault("meta", {})
        data["meta"].update({"model": settings.model, "latency_ms": latency_ms, "mode": "llm"})
        return ScoreResponse(**data)
    except Exception as e:
        logger.error("parse json error: %s; content=%s", e, content[:300])
        return _mock_score(req)

