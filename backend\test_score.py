import asyncio
from app.main import score
from app.schemas import ScoreRequest

async def run_tests():
    # Mock mode
    req_mock = ScoreRequest(
        exam="ielts",
        prompt_text="Some people think ... To what extent do you agree or disagree?",
        essay_text="People is increasingly concerned about health. On the other hand, ...",
        debug_mock=True,
    )
    res1 = await score(req_mock)
    print("MOCK MODE:", res1.dict())

    # Real mode (will fallback to mock if LLM调用失败)
    req_real = ScoreRequest(
        exam="toefl",
        prompt_text="Do you agree or disagree with the following statement? ...",
        essay_text="In my opinion, university students should be required ...",
        debug_mock=False,
    )
    res2 = await score(req_real)
    print("REAL MODE (with fallback):", res2.dict())

if __name__ == "__main__":
    asyncio.run(run_tests())

