from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

payload_mock = {
    "exam": "ielts",
    "prompt_text": "Some people think ... To what extent do you agree or disagree?",
    "essay_text": "People is increasingly concerned about health. On the other hand, more people still ignore ...",
    "debug_mock": True,
}

payload_real = {
    "exam": "toefl",
    "prompt_text": "Do you agree or disagree with the following statement? ...",
    "essay_text": "In my opinion, university students should be required to take at least one class about ...",
    "debug_mock": False,
}

print("--- Testing MOCK mode ---")
res1 = client.post("/api/submissions/score", json=payload_mock)
print("status:", res1.status_code)
print("overall:", res1.json().get("overall_score"))
print("dims count:", len(res1.json().get("dimensions", [])))

print("--- Testing REAL mode (will fallback if network fails) ---")
res2 = client.post("/api/submissions/score", json=payload_real)
print("status:", res2.status_code)
print("overall:", res2.json().get("overall_score"))
print("dims count:", len(res2.json().get("dimensions", [])))

