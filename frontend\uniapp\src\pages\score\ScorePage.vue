<template>
  <view class="container">
    <view class="card form">
      <picker :range="examOptions" :value="examIndex" @change="onExamChange">
        <view class="picker">考试：{{ examOptions[examIndex] }}</view>
      </picker>
      <uni-easyinput v-model="promptText" placeholder="请输入题干（Task 2 / Independent Writing）" />
      <uni-easyinput type="textarea" autoHeight v-model="essayText" placeholder="粘贴或输入你的作文..." />
      <view class="btns">
        <button type="default" :loading="loading" @click="submit(true)">模拟评分</button>
        <button type="primary" :loading="loading" @click="submit(false)">真实评分</button>
      </view>
    </view>

    <view v-if="result" class="card">
      <view class="title">总分：{{ result.overall_score }}</view>

      <!-- 维度图表 -->
      <view id="chart" class="chart" />

      <!-- 正文与问题高亮 -->
      <view class="sub-title">正文与高亮</view>
      <view class="essay" v-html="highlightedEssay"></view>

      <view class="sub-title">问题定位（列表）</view>
      <view v-if="result.issues?.length">
        <view class="issue" v-for="(it, idx) in result.issues" :key="idx">
          <text>句 {{ it.sentence_index }} ｜ {{ it.category }} ｜ {{ it.severity }}</text>
          <text class="comment">{{ it.comment }}</text>
          <text class="evidence">“{{ it.evidence }}”</text>
        </view>
      </view>
      <view v-else>未检测到问题</view>

      <view class="sub-title">改写建议</view>
      <view v-if="result.suggestions?.length">
        <view class="sugg" v-for="(s, idx) in result.suggestions" :key="idx">
          <text>级别：{{ s.level }} ｜ 目标：{{ s.target_indices?.join(', ') }}</text>
          <text class="rewrite">建议：{{ s.rewrite }}</text>
          <text class="rationale">说明：{{ s.rationale }}</text>
        </view>
      </view>
      <view v-else>暂无建议</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { BACKEND_URL } from '@/utils/config'

const examOptions = ['ielts','toefl']
const examIndex = ref(0)
const promptText = ref('')
const essayText = ref('')
const loading = ref(false)
const result = ref<any>(null)
const highlightedEssay = ref('')

function onExamChange(e: any){ examIndex.value = Number(e.detail.value) }

let chart: any = null
function renderChart(){
  if(!result.value) return
  const el = document.getElementById('chart') as any
  if(!el) return
  if(!chart) chart = echarts.init(el)
  const dims = result.value.dimensions || []
  const option = {
    tooltip: {},
    xAxis: { type: 'category', data: dims.map((d:any)=>d.key) },
    yAxis: { type: 'value', min: 0, max: Math.max(...dims.map((d:any)=>d.max_score||9), 9) },
    series: [{ type: 'bar', data: dims.map((d:any)=>d.score) }]
  }
  chart.setOption(option)
}

function buildHighlights(){
  const text = essayText.value
  if(!result.value || !text) { highlightedEssay.value = escapeHtml(text); return }
  const ranges = (result.value.issues||[])
    .map((it:any)=>({start:it.start_char, end:it.end_char, title:`${it.category}｜${it.severity}｜${it.comment}`}))
    .filter((r:any)=>r.start>=0 && r.end>r.start && r.end<=text.length)
    .sort((a:any,b:any)=>a.start-b.start)
  // 合并重叠区间
  const merged:any[] = []
  for(const r of ranges){
    if(!merged.length || r.start > merged[merged.length-1].end){ merged.push({...r}) }
    else { merged[merged.length-1].end = Math.max(merged[merged.length-1].end, r.end) }
  }
  let html = ''
  let cursor = 0
  for(const r of merged){
    if(r.start > cursor){ html += escapeHtml(text.slice(cursor, r.start)) }
    html += `<mark title="${escapeHtml(r.title)}" class="hl">${escapeHtml(text.slice(r.start, r.end))}</mark>`
    cursor = r.end
  }
  if(cursor < text.length){ html += escapeHtml(text.slice(cursor)) }
  highlightedEssay.value = html
}

function escapeHtml(s:string){
  return (s||'').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;')
}

watch(result, async ()=>{ await nextTick(); renderChart(); buildHighlights(); })
watch(essayText, ()=>{ buildHighlights() })

onMounted(()=>{ buildHighlights() })

async function submit(mock:boolean){
  if(!promptText.value || !essayText.value) return uni.showToast({title:'请填写题干与作文', icon:'none'})
  loading.value = true
  try{
    const res = await uni.request({
      url: `${BACKEND_URL}/api/submissions/score`,
      method: 'POST',
      data: {
        exam: examOptions[examIndex.value],
        prompt_text: promptText.value,
        essay_text: essayText.value,
        debug_mock: mock
      }
    })
    if(res.statusCode === 200){
      result.value = res.data as any
    } else {
      uni.showToast({title: '评分失败', icon:'none'})
    }
  } catch(err:any){
    uni.showToast({title: err?.message || '请求出错', icon:'none'})
  } finally { loading.value = false }
}
</script>

<style scoped>
.container{ padding: 12px; }
.card{ background:#fff; padding:12px; border-radius:8px; margin-bottom:12px; }
.form .btns{ display:flex; gap:8px; margin-top:8px; }
.title{ font-weight:600; font-size:16px; margin-bottom:8px; }
.sub-title{ margin-top:12px; font-weight:600; }
.chart{ width:100%; height:220px; }
.dim{ margin: 6px 0; }
.issue, .sugg{ margin:8px 0; padding:8px; background:#fafafa; border-radius:6px; }
.rationale, .comment, .evidence, .rewrite{ display:block; color:#666; margin-top:4px; }
.essay{ white-space:pre-wrap; line-height:1.6; }
.hl{ background: #ffe6a7; padding: 0 2px; }
</style>

