# Writing Coach API

## 启动

```bash
cd backend
python -m venv .venv
.venv/Scripts/pip install -r requirements.txt
# 复制环境变量
copy .env.example .env
# 启动服务（端口 8787）
.venv/Scripts/python -m uvicorn app.main:app --host 127.0.0.1 --port 8787
```

## 接口
- POST /api/submissions/score
  - body: { exam, prompt_text, essay_text, debug_mock }
  - 返回: ScoreResponse（包含 dimensions/issues/suggestions/meta）

## 调试
- /health 查看模型与网关配置
- 出错时后端将自动回退 mock，日志打印错误详情

